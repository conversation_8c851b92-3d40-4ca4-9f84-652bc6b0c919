<template>
	<view class="container">
		<view class="decoration"></view>
		
		<!-- 顶部标题 -->
		<view class="header">
			<text class="header-title">健康圈子</text>
		</view>

		<!-- 主要内容 -->
		<view class="main-content">
			<!-- 标签切换 -->
			<view class="tab-container">
				<view 
					class="tab" 
					:class="{ active: activeTab === 'recommend' }" 
					@tap="switchTab('recommend')"
				>
					<text>推荐</text>
				</view>
				<view 
					class="tab" 
					:class="{ active: activeTab === 'follow' }" 
					@tap="switchTab('follow')"
				>
					<text>关注</text>
				</view>
				<view 
					class="tab" 
					:class="{ active: activeTab === 'hot' }" 
					@tap="switchTab('hot')"
				>
					<text>热门</text>
				</view>
			</view>

			<!-- 帖子列表 -->
			<view class="post-list">
				<view class="post-card" v-for="(post, index) in postList" :key="index">
					<!-- 帖子头部 -->
					<view class="post-header">
						<image class="user-avatar" :src="post.userAvatar" mode="aspectFill"></image>
						<view class="user-info">
							<text class="user-name">{{ post.userName }}</text>
							<text class="post-time">{{ post.postTime }}</text>
						</view>
					</view>
					
					<!-- 帖子内容 -->
					<view class="post-content">
						<text>{{ post.content }}</text>
					</view>
					
					<!-- 帖子图片 -->
					<view class="post-images" v-if="post.images && post.images.length > 0">
						<image 
							class="post-image" 
							v-for="(image, imgIndex) in post.images" 
							:key="imgIndex"
							:src="image" 
							mode="aspectFill"
						></image>
					</view>
					
					<!-- 帖子操作 -->
					<view class="post-actions">
						<view class="action-button" @tap="likePost(index)">
							<text class="action-icon">👍</text>
							<text class="action-text">{{ post.likeCount }}</text>
						</view>
						<view class="action-button" @tap="commentPost(index)">
							<text class="action-icon">💬</text>
							<text class="action-text">{{ post.commentCount }}</text>
						</view>
						<view class="action-button" @tap="sharePost(index)">
							<text class="action-icon">↗️</text>
							<text class="action-text">分享</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	const util = require("@/utils/util.js");
	const api = require('@/utils/api.js');
	
	export default {
		data() {
			return {
				activeTab: 'recommend',
				postList: [],
				page: 1,
				size: 10,
				hasMore: true
			}
		},
		
		methods: {
			// 切换标签
			switchTab(tab) {
				this.activeTab = tab;
				this.page = 1;
				this.hasMore = true;
				this.postList = [];
				this.getCommunityPosts();
			},
			
			// 获取圈子帖子
			getCommunityPosts() {
				let that = this;
				
				uni.showLoading({
					title: '加载中...'
				});
				
				// 这里使用模拟数据，实际项目中应该调用真实API
				// util.request(api.CommunityPosts, {
				// 	page: that.page,
				// 	size: that.size,
				// 	tab: that.activeTab
				// }).then(function(res) {
				// 	if (res.errno === 0) {
				// 		if (that.page === 1) {
				// 			that.postList = res.data.datalist;
				// 		} else {
				// 			that.postList = that.postList.concat(res.data.datalist);
				// 		}
				// 		that.hasMore = res.data.hasMore;
				// 	}
				// 	uni.hideLoading();
				// });
				
				// 模拟数据
				setTimeout(() => {
					const mockData = that.getMockPosts();
					if (that.page === 1) {
						that.postList = mockData;
					} else {
						that.postList = that.postList.concat(mockData);
					}
					that.hasMore = that.page < 3; // 模拟只有3页数据
					uni.hideLoading();
				}, 1000);
			},
			
			// 点赞帖子
			likePost(index) {
				// util.request(api.CommunityPostLike, {
				// 	postId: this.postList[index].id
				// }).then(function(res) {
				// 	if (res.errno === 0) {
				// 		// 更新点赞数
				// 	}
				// });
				
				// 模拟点赞
				this.postList[index].likeCount++;
				uni.showToast({
					title: '点赞成功',
					icon: 'success'
				});
			},
			
			// 评论帖子
			commentPost(index) {
				uni.showToast({
					title: '评论功能开发中',
					icon: 'none'
				});
			},
			
			// 分享帖子
			sharePost(index) {
				uni.showToast({
					title: '分享功能开发中',
					icon: 'none'
				});
			},
			
			// 获取模拟数据
			getMockPosts() {
				return [
					{
						id: 1,
						userName: '王医生',
						userAvatar: '/static/images/noportait.png',
						postTime: '2小时前',
						content: '今天给大家分享一个简单的养生小妙招：每天早晚各按摩涌泉穴5分钟，可以改善睡眠质量，缓解疲劳。具体方法如下...',
						images: [
							'/static/images/noportait.png',
							'/static/images/noportait.png',
							'/static/images/noportait.png'
						],
						likeCount: 128,
						commentCount: 36
					},
					{
						id: 2,
						userName: '李医生',
						userAvatar: '/static/images/noportait.png',
						postTime: '5小时前',
						content: '最近很多患者咨询关于春季养生的问题，我整理了一些实用的建议，希望对大家有帮助...',
						images: [],
						likeCount: 96,
						commentCount: 24
					}
				];
			}
		},
		
		// 下拉刷新
		onPullDownRefresh() {
			this.page = 1;
			this.hasMore = true;
			this.postList = [];
			this.getCommunityPosts();
			setTimeout(() => {
				uni.stopPullDownRefresh();
			}, 1000);
		},
		
		// 上拉加载更多
		onReachBottom() {
			if (this.hasMore) {
				this.page++;
				this.getCommunityPosts();
			}
		},
		
		onLoad: function() {
			this.getCommunityPosts();
		}
	}
</script>

<style scoped>
	* {
		margin: 0;
		padding: 0;
		box-sizing: border-box;
	}

	.container {
		max-width: 414px;
		margin: 0 auto;
		background: #fff;
		min-height: 100vh;
		position: relative;
	}

	.decoration {
		position: absolute;
		width: 100%;
		height: 100%;
		pointer-events: none;
		opacity: 0.1;
		background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M50,0 L100,50 L50,100 L0,50 Z" fill="%238B4513"/></svg>');
		background-size: 50px 50px;
	}

	.header {
		background: #8B4513;
		color: #fff;
		padding: 15px;
		text-align: center;
		position: relative;
		background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M0,0 L100,0 L100,100 L0,100 Z" fill="none" stroke="%23D2B48C" stroke-width="2"/><path d="M20,20 L80,20 L80,80 L20,80 Z" fill="none" stroke="%23D2B48C" stroke-width="1"/><path d="M40,40 L60,40 L60,60 L40,60 Z" fill="none" stroke="%23D2B48C" stroke-width="0.5"/></svg>');
		background-size: 20px 20px;
		border-bottom: 2px solid #D2B48C;
	}

	.header-title {
		font-size: 24px;
		letter-spacing: 4px;
		text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
	}

	.main-content {
		padding: 20px;
	}

	.tab-container {
		display: flex;
		border-bottom: 1px solid #D2B48C;
		margin-bottom: 20px;
	}

	.tab {
		flex: 1;
		text-align: center;
		padding: 10px;
		color: #4A4A4A;
		transition: all 0.3s ease;
	}

	.tab.active {
		color: #8B4513;
		border-bottom: 2px solid #8B4513;
	}

	.post-list {
		/* 帖子列表容器 */
	}

	.post-card {
		background: #fff;
		border: 1px solid #D2B48C;
		border-radius: 8px;
		padding: 15px;
		margin-bottom: 15px;
	}

	.post-header {
		display: flex;
		align-items: center;
		margin-bottom: 10px;
	}

	.user-avatar {
		width: 40px;
		height: 40px;
		border-radius: 50%;
		margin-right: 10px;
		border: 1px solid #D2B48C;
	}

	.user-info {
		flex: 1;
	}

	.user-name {
		font-weight: bold;
		color: #8B4513;
		display: block;
		margin-bottom: 2px;
	}

	.post-time {
		font-size: 12px;
		color: #666;
		display: block;
	}

	.post-content {
		margin-bottom: 15px;
		line-height: 1.6;
		color: #4A4A4A;
	}

	.post-images {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 5px;
		margin-bottom: 15px;
	}

	.post-image {
		width: 100%;
		aspect-ratio: 1;
		border-radius: 4px;
	}

	.post-actions {
		display: flex;
		justify-content: space-around;
		border-top: 1px solid #D2B48C;
		padding-top: 10px;
	}

	.action-button {
		display: flex;
		align-items: center;
		gap: 5px;
		color: #4A4A4A;
		font-size: 14px;
	}

	.action-icon {
		font-size: 16px;
	}

	.action-text {
		font-size: 14px;
	}
</style>
